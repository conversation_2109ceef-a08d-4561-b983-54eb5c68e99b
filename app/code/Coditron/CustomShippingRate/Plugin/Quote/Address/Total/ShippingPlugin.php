<?php
/**
 * Copyright © Coditron Technologies All rights reserved.
 * See COPYING.txt for license details.
 * http://www.coditron.com | <EMAIL>
 */

namespace Coditron\CustomShippingRate\Plugin\Quote\Address\Total;

use Magento\Quote\Api\Data\ShippingAssignmentInterface;
use Magento\Quote\Model\Quote;
use Magento\Quote\Model\Quote\Address;
use Magento\Quote\Model\Quote\Address\Rate;
use Magento\Quote\Model\Quote\Address\Total;
use Magento\Quote\Model\Quote\Address\Total\Shipping;
use Coditron\CustomShippingRate\Helper\Data;
use Coditron\CustomShippingRate\Model\Carrier;
use Coditron\CustomShippingRate\Api\ShipTableRatesRepositoryInterface;
use Magento\Quote\Model\Quote\Address\RateFactory;

class ShippingPlugin
{
    /**
     * @var Data
     */
    protected $customShippingRateHelper;

    /**
     * @var ShipTableRatesRepositoryInterface
     */
    protected $shipTableRatesRepository;

    /**
     * @var RateFactory
     */
    protected $rateFactory;

    /**
     * @param Data $customShippingRateHelper
     * @param ShipTableRatesRepositoryInterface $shipTableRatesRepository
     * @param RateFactory $rateFactory
     */
    public function __construct(
        Data $customShippingRateHelper,
        ShipTableRatesRepositoryInterface $shipTableRatesRepository,
        RateFactory $rateFactory
    ) {
        $this->customShippingRateHelper = $customShippingRateHelper;
        $this->shipTableRatesRepository = $shipTableRatesRepository;
        $this->rateFactory = $rateFactory;
    }

    /**
     * @param Shipping $subject
     * @param callable $proceed
     * @param Quote $quote
     * @param ShippingAssignmentInterface $shippingAssignment
     * @param Total $total
     * @return mixed
     */
    public function aroundCollect(
        Shipping $subject,
        callable $proceed,
        Quote $quote,
        ShippingAssignmentInterface $shippingAssignment,
        Total $total
    ) {
        $shipping = $shippingAssignment->getShipping();
        $address = $shipping->getAddress();
        $method = $address->getShippingMethod();
        $storeId = $quote->getStoreId();

        if (!$this->customShippingRateHelper->isEnabled($storeId)
            || $address->getAddressType() != Address::ADDRESS_TYPE_SHIPPING
            || (strpos((string) $method, Carrier::CODE) === false && strpos((string) $method, 'freeshipping_threshold_') === false && strpos((string) $method, '_free') === false)
        ) {
            return $proceed($quote, $shippingAssignment, $total);
        }

        $customShippingOption = $this->getCustomShippingJsonToArray($method, $address, $storeId);

        if ($customShippingOption && strpos((string) $method, $customShippingOption['code']) !== false) {
            //update shipping code
            $shipping->setMethod($customShippingOption['code']);
            $address->setShippingMethod($customShippingOption['code']);

            // For free shipping thresholds, create a fake rate if it doesn't exist
            if (strpos($method, 'freeshipping_threshold_') === 0) {
                $this->ensureFreeShippingThresholdRate($address, $customShippingOption);
            } elseif (strpos($method, '_free') !== false) {
                $this->ensureFreeShippingRate($address, $customShippingOption);
            }

            $this->updateCustomRate($address, $customShippingOption);

            // For free shipping thresholds, ensure totals are recalculated
            if (strpos($method, 'freeshipping_threshold_') === 0 || strpos($method, '_free') !== false) {
                $total->setShippingAmount(0);
                $total->setBaseShippingAmount(0);
                $total->setShippingDescription($customShippingOption['description']);
            }
        }

        return $proceed($quote, $shippingAssignment, $total);
    }

    /**
     * @param $address
     * @param $customShippingOption
     */
    protected function updateCustomRate($address, $customShippingOption)
    {
        if ($selectedRate = $this->getSelectedShippingRate($address, $customShippingOption['code'])) {
            $cost = (float) $customShippingOption['rate'];
            $description = trim($customShippingOption['description']);

            $selectedRate->setPrice($cost);
            $selectedRate->setCost($cost);
            //Empty by default. Use in third-party modules
            if (!empty($description) || strlen($description) > 2) {
                $selectedRate->setMethodTitle($description);
                //TODO: check if the above has any effect / if passed by reference.. or gets lost
            }
        }
    }

    /**
     * @param $json
     * @param $address
     * @param null $storeId
     * @return array|bool
     */
    private function getCustomShippingJsonToArray($json, $address, $storeId = null)
    {
        $isJson = $this->customShippingRateHelper->isJson($json);

        //reload exist shipping cost if custom shipping method
        if ($json && !$isJson) {
            $rate = 0;
            if ($selectedRate = $this->getSelectedShippingRate($address, $json)) {
                $rate = $selectedRate->getPrice();
            }

            // Handle free shipping threshold codes
            if (strpos($json, 'freeshipping_threshold_') === 0) {
                $description = 'Free Shipping';
                $thresholdId = str_replace('freeshipping_threshold_', '', $json);

                try {
                    if (is_numeric($thresholdId)) {
                        $threshold = $this->shipTableRatesRepository->get((int)$thresholdId);
                        $description = $threshold->getCourier() ?: 'Free Shipping';
                    }
                } catch (\Exception $e) {
                    // Use default description if threshold not found
                }

                $jsonToArray = [
                    'code' => $json,
                    'type' => 'freeshipping_threshold',
                    'rate' => 0, // Free shipping
                    'description' => $description
                ];
            } elseif (strpos($json, '_free') !== false) {
                // Handle _free suffix codes (e.g., customshippingrate_express_free)
                $baseCode = str_replace('_free', '', $json);
                if ($selectedRate = $this->getSelectedShippingRate($address, $baseCode)) {
                    $description = $selectedRate->getMethodTitle() . ' (Free Shipping)';
                } else {
                    $description = 'Free Shipping';
                }

                $jsonToArray = [
                    'code' => $json,
                    'type' => 'free_shipping',
                    'rate' => 0, // Free shipping
                    'description' => $description
                ];
            } else {
                $jsonToArray = [
                    'code' => $json,
                    'type' => $this->customShippingRateHelper->getShippingCodeFromMethod($json, $storeId),
                    'rate' => $rate
                ];
            }

            return $this->formatShippingArray($jsonToArray);
        }

        $jsonToArray = (array)json_decode($json, true);

        if (is_array($jsonToArray) && count($jsonToArray) == 4) {
            return $this->formatShippingArray($jsonToArray);
        }

        return false;
    }

    /**
     * @param $address
     * @param $code
     * @return null | Rate
     */
    protected function getSelectedShippingRate($address, $code)
    {
        $selectedRate = null;

        if ($code) {
            foreach ($address->getAllShippingRates() as $rate) {
                if ($rate->getCode() == $code) {
                    $selectedRate = $rate;
                    break;
                }
            }
        }

        return $selectedRate;
    }

    /**
     * Create a fake shipping rate for free shipping thresholds if it doesn't exist
     *
     * @param $address
     * @param $customShippingOption
     */
    protected function ensureFreeShippingThresholdRate($address, $customShippingOption)
    {
        $code = $customShippingOption['code'];

        // Check if rate already exists
        if ($this->getSelectedShippingRate($address, $code)) {
            return;
        }

        // Extract threshold ID from code (freeshipping_threshold_123)
        $thresholdId = str_replace('freeshipping_threshold_', '', $code);
        $methodTitle = 'Free Shipping';

        try {
            if (is_numeric($thresholdId)) {
                $threshold = $this->shipTableRatesRepository->get((int)$thresholdId);
                $methodTitle = $threshold->getCourier() ?: 'Free Shipping';
            }
        } catch (\Exception $e) {
            // Use default title if threshold not found
        }

        // Create a fake rate for the free shipping threshold
        $rate = $this->rateFactory->create();
        $rate->setCode($code);
        $rate->setCarrier('customfreeshipping');
        $rate->setCarrierTitle('Free Shipping');
        $rate->setMethod('threshold');
        $rate->setMethodTitle($methodTitle);
        $rate->setPrice(0);
        $rate->setCost(0);

        // Add the rate to the address
        $address->addShippingRate($rate);
    }

    /**
     * Create a fake shipping rate for _free suffix codes if it doesn't exist
     *
     * @param $address
     * @param $customShippingOption
     */
    protected function ensureFreeShippingRate($address, $customShippingOption)
    {
        $code = $customShippingOption['code'];

        // Check if rate already exists
        if ($this->getSelectedShippingRate($address, $code)) {
            return;
        }

        // Create a fake rate for the free shipping
        $rate = $this->rateFactory->create();
        $rate->setCode($code);
        $rate->setCarrier('customfreeshipping');
        $rate->setCarrierTitle('Free Shipping');
        $rate->setMethod('free');
        $rate->setMethodTitle($customShippingOption['description'] ?: 'Free Shipping');
        $rate->setPrice(0);
        $rate->setCost(0);

        // Add the rate to the address
        $address->addShippingRate($rate);
    }

    /**
     * @param $jsonToArray array
     * @return array
     */
    protected function formatShippingArray($jsonToArray)
    {
        $customShippingOption = [
            'code' => '',
            'rate' => 0,
            'type' => '',
            'description' => ''
        ];

        foreach ((array) $jsonToArray as $key => $value) {
            $customShippingOption[$key] = $value;
        }

        return $customShippingOption;
    }
}
